@import "./../common/abstracts/_mixin.scss";
@import "./../common/abstracts/variable.scss";

.wot-theme-dark {
  @include b(datetime-picker) {

    @include e(placeholder) {
      color: $-dark-color-gray;
    }

    :deep(.wd-datetime-picker__arrow),
    :deep(.wd-datetime-picker__clear) {
      color: $-dark-color;
    }

    @include e(action) {
      @include m(cancel) {
        color: $-dark-color;
      }
    }

    @include e(region) {
      color: $-dark-color;
  
      @include when(active) {
        background: $-picker-region-bg-active-color;
        color: $-dark-color;
      }
    }
  }
}
 

@include b(datetime-picker) {
  @include edeep(cell) {
    @include when(disabled) {
      .wd-cell__value {
        color: $-input-disabled-color;
        cursor: not-allowed;
      }
    }
    @include when(error) {
      .wd-cell__value {
        color: $-input-error-color;
      }
      .wd-datetime-picker__arrow {
        color: $-input-error-color;
      }
    }
    @include when(large) {
      .wd-datetime-picker__arrow {
        font-size: $-cell-icon-size-large;
      }
    }
    
    .wd-cell__value--ellipsis {
      view {
        @include lineEllipsis;
        width: 100%;
      }
      
      text {
        @include lineEllipsis;
        max-width: 100%;
      }
    }
  }

  @include e(placeholder) {
    color: $-input-placeholder-color;
  }
  
  @include edeep(arrow) {
    display: block;
    font-size: $-cell-icon-size;
    color: $-cell-arrow-color;
    line-height: $-cell-line-height;
  }

  @include edeep(clear) {
    display: block;
    font-size: $-cell-icon-size;
    color: $-cell-clear-color;
    line-height: $-cell-line-height;
  }

  @include edeep(popup) {
    border-radius: 16px 16px 0px 0px;
  }

  @include e(wraper) {
    padding-bottom: var(--window-bottom);
  }

  @include e(toolbar) {
    position: relative;
    display: flex;
    font-size: $-picker-toolbar-fs;
    height: $-picker-toolbar-height;
    line-height: $-picker-action-height;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
  }

  @include e(action) {
    display: block;
    border: none;
    outline: none;
    font-size: $-picker-toolbar-fs;
    color: $-picker-toolbar-finish-color;
    background: transparent;
    padding: 24px 15px 14px 15px;

    @include m(cancel) {
      color: $-picker-toolbar-cancel-color;
    }

    @include when(loading) {
      color: $-picker-loading-button-color;
    }
  }
  @include e(title) {
    display: block;
    float: 1;
    color: $-picker-toolbar-title-color;
  }

  @include e(region-tabs) {
    display: flex;
  }

  @include e(region) {
    width: 50%;
    display: inline-block;
    color: $-picker-region-color;
    text-align: center;
    padding: 14px 0;
    font-size: $-picker-region-fs;
    line-height: 16px;
    transition: all 0.15s ease-out;

    @include when(active) {
      background: $-picker-region-bg-active-color;
      color: $-color-white;
    }
  }

  @include e(region-time) {
    font-size: 16px;
    margin-top: 2px;
  }

  @include e(hidden) {
    visibility: hidden;
    overflow: hidden;
    height: 0;
  }

  @include e(show) {
    visibility: visible;
    height: auto;
  }
}
