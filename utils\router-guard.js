/**
 * 路由守卫
 */
import { isLoggedIn, redirectToLogin } from './auth'

// 不需要登录的页面
const whiteList = ['/pages/login/login']

/**
 * 检查页面访问权限
 * @param {String} url 页面路径
 * @returns {Boolean} 是否允许访问
 */
export function checkPageAccess(url) {
  // 白名单页面直接允许访问
  if (whiteList.some((path) => url.includes(path))) {
    return true
  }

  // 其他页面需要登录
  if (!isLoggedIn()) {
    redirectToLogin()
    return false
  }

  return true
}

/**
 * 页面跳转前的权限检查
 * @param {String} url 目标页面路径
 * @returns {Boolean} 是否允许跳转
 */
export function beforePageJump(url) {
  return checkPageAccess(url)
}
