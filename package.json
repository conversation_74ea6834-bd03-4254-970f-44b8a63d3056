{"name": "business-card-mini-program", "version": "1.0.0", "description": "名片小程序", "main": "main.js", "scripts": {"lint": "eslint --ext .js,.vue .", "lint:fix": "eslint --ext .js,.vue . --fix", "format": "prettier --write \"**/*.{js,vue,json,css,scss,html,md}\""}, "keywords": ["uni-app", "vue3", "miniprogram", "business-card"], "author": "", "license": "MIT", "devDependencies": {"eslint": "^8.57.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-vue": "^10.4.0", "prettier": "^3.6.2"}, "dependencies": {"crypto-js": "^4.2.0", "jsencrypt": "^3.5.4", "pinia": "^2.1.7", "wot-design-uni": "^1.12.2"}}