<script>
import { isLoggedIn, redirectToLogin } from '@/utils/auth'

export default {
  onLaunch: function () {
    console.log('App Launch')

    // 检查登录状态
    this.checkLoginStatus()
  },
  onShow: function () {
    console.log('App Show')
  },
  onHide: function () {
    console.log('App Hide')
  },
  methods: {
    // 检查登录状态
    checkLoginStatus() {
      // 获取当前页面路径
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage ? currentPage.route : ''

      // 如果当前不在登录页且未登录，则跳转到登录页
      if (!currentRoute.includes('login') && !isLoggedIn()) {
        setTimeout(() => {
          redirectToLogin()
        }, 100)
      }
    },
  },
}
</script>

<style lang="scss">
/*每个页面公共css */
@import '@/static/iconfont/iconfont.css';
@import '@/static/parameters.scss';
@import '@/static/free.scss';
// 设置整个项目的背景色
page {
  // background-color: $light;
  font-size: 32rpx;
}
.themePage {
  position: relative;
  z-index: 5;
  background: radial-gradient(circle at 10% 2%, #fbfbf5, #edf4f5, #fff, #fff);
}
/*form 一些通用样式*/
.normal-button-rounded {
  $h: 80rpx;
  height: $h;
  border-radius: $h;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

/*每个页面公共css */
</style>
