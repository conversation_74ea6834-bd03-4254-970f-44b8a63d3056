@import "../common/abstracts/variable.scss";
@import "../common/abstracts/_mixin.scss";

@include b(video-preview) {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: $-video-preview-bg;

  @include e(video) {
    width: 100%;
    height: 242px;
    transition: all 0.3s ease;
  }

  @include edeep(close) {
    position: absolute;
    box-sizing: border-box;
    top: 0px;
    right: 0px;
    padding: 12px;
    text-align: center;
    cursor: pointer;
    font-size: $-video-preview-close-font-size;
    color: $-video-preview-close-color;
  }
}