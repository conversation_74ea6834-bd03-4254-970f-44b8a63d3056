<template>
  <view class="wrap mheight-100vh themePage box-border flex flex-column">
    <navigationBar title="我的名片" :showBack="false" classname="mb-1 flex-shrink" />
    <view class="p-4 mt-2 flex-1 flex">
      <view class="picture-wrap flex-1 flex flex-column">
        <view class="position-relative flex-1 picture-box rounded-12 overflow-hidden">
          <view class="position-absolute top-0 right-0 left-0 bottom-0 zindex-1">
            <image :src="picUrl" mode="aspectFill" class="width-100 height-100"></image>
          </view>
          <view
            class="position-absolute left-0 top-0 right-0 bottom-0 zindex-2 p-base flex flex-column"
          >
            <view class="flex-shrink flex justify-center">
              <image src="" mode=""></image>
            </view>
          </view>
        </view>
        <view class="flex-shrink px-4">
          <view class="border1"></view>
          <view class="px-4">
            <view class="border2"></view>
          </view>
        </view>
      </view>
    </view>
    <view class="flex-shrink flex justify-center mb-4">
      <view
        hover-class="bg-hover"
        class="build-btn border py-2 text-center bg-white border-theme-brand text-theme-brand font-14 bg-white rounded-20"
        >制作我的名片</view
      >
    </view>
  </view>
</template>

<script setup>
import navigationBar from '@/components/common/navigationBar.vue'
import { ref } from 'vue'
const picUrl = ref('https://demo.easeidea.com/minio/easeidea-ai/2025/08/19/lisa/<EMAIL>')
</script>

<style lang="scss" scoped>
.picture-wrap {
  $left: 40rpx;
  $radius: 24rpx;
  position: relative;
  .picture-box {
    position: relative;
    z-index: 5;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.5);
  }
  .border1 {
    width: 100%;
    border-bottom-left-radius: $radius;
    border-bottom-right-radius: $radius;
    height: 48rpx;
    background-color: #d6dee2;
    box-shadow: -2rpx 2rpx 4rpx 0rpx rgba(#ffffff, 0.35);
  }
  .border2 {
    width: 100%;
    border-bottom-left-radius: $radius;
    border-bottom-right-radius: $radius;
    height: 44rpx;
    background-color: #f2f5f6;
    box-shadow: -2rpx 2rpx 4rpx 0rpx rgba(#ffffff, 0.35);
  }
}
.build-btn {
  width: 400rpx;
}
</style>
