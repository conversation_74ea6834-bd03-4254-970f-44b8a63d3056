module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: ['eslint:recommended', 'prettier'],
  ignorePatterns: [
    'node_modules/',
    'uni_modules/',
    'unpackage/',
    'dist/',
    '*.min.js',
    'static/iconfont/',
  ],
  parser: 'vue-eslint-parser',
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'module',
  },
  plugins: ['prettier'],
  globals: {
    uni: 'readonly',
    wx: 'readonly',
    __wxConfig: 'readonly',
    getCurrentPages: 'readonly',
  },
  rules: {
    'prettier/prettier': [
      'warn',
      {
        endOfLine: 'auto',
      },
    ],
    'no-console': 'off',
    'no-unused-vars': 'warn',
  },
}
