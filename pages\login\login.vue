<template>
  <view class="login-container">
    <!-- 状态栏占位 -->
    <view :style="{ height: statusBarHeight + 'px' }"></view>

    <!-- 头部 -->
    <view class="header">
      <view class="title">EaseCard</view>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <!-- 显示登录参数信息 -->
      <view class="login-info">
        <view class="info-item">
          <text class="label">客户端ID:</text>
          <text class="value">{{ loginForm.clientId }}</text>
        </view>
        <view class="info-item">
          <text class="label">授权类型:</text>
          <text class="value">{{ loginForm.grantType }}</text>
        </view>
        <view class="info-item">
          <text class="label">租户ID:</text>
          <text class="value">{{ loginForm.tenantId || '待获取' }}</text>
        </view>
        <view class="info-item">
          <text class="label">小程序Code:</text>
          <text class="value">{{ loginForm.xcxCode || '待获取' }}</text>
        </view>
      </view>

      <!-- 获取Code按钮 -->
      <view class="form-item">
        <wd-button type="info" size="large" @click="getXcxCode" block> 获取小程序Code </wd-button>
      </view>

      <!-- resolved 加个button 查看租户列表 -->
      <view class="form-item" v-if="loginForm.xcxCode">
        <wd-button type="info" size="large" @click="showTenantListInfo" block>
          查看租户列表
        </wd-button>
      </view>
      <!-- 租户选择 -->
      <view class="form-item" v-if="showTenantSelector">
        <view class="tenant-selector">
          <view class="selector-title">请选择租户</view>
          <view class="tenant-list">
            <view
              v-for="tenant in tenantList"
              :key="tenant.tenantId"
              class="tenant-item"
              :class="{ active: loginForm.tenantId === tenant.tenantId }"
              @click="selectTenant(tenant)"
            >
              <view class="tenant-name">{{ tenant.companyName }}</view>
              <view class="tenant-id">ID: {{ tenant.tenantId }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 登录按钮 -->
      <view class="form-item">
        <wd-button
          type="primary"
          size="large"
          :loading="loading"
          :disabled="!loginForm.tenantId"
          @click="handleLogin"
          block
        >
          {{ loading ? '登录中...' : '登录' }}
        </wd-button>
      </view>
    </view>

    <!-- 底部 -->
    <view class="footer">
      <text>Copyright © 2018-2025 EaseCard All Rights Reserved.</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { getMiniAppTenantList } from '@/api/service/login'

const userStore = useUserStore()

// 状态栏高度
const statusBarHeight = ref(0)

// 登录表单数据
// resolved 登录参数要求为以下四个：clientId、grantType、tenantId、xcxCode（传递小程序获取的code），其余不需要，点击登录时console这四个参数便于调试
const loginForm = ref({
  clientId: '428a8310cd442757ae699df5d894f051',
  grantType: 'xcx',
  tenantId: '', // 通过接口获取
  xcxCode: '', // 小程序获取的code
})

// 租户相关状态
const tenantList = ref([])
const showTenantSelector = ref(false)

// 页面状态
const loading = ref(false)

/**
 * 获取小程序code
 */
const getXcxCode = () => {
  return new Promise((resolve, reject) => {
    uni.login({
      provider: 'weixin',
      success: (res) => {
        console.log('获取小程序code成功:', res.code)
        loginForm.value.xcxCode = res.code
        uni.showToast({
          title: 'Code获取成功',
          icon: 'success',
        })
        resolve(res.code)
      },
      fail: (error) => {
        console.error('获取小程序code失败:', error)
        uni.showToast({
          title: 'Code获取失败',
          icon: 'none',
        })
        reject(error)
      },
    })
  })
}

/**
 * 获取租户列表
 */
const getTenantList = async (code) => {
  try {
    console.log('获取租户列表，code:', code)
    const res = await getMiniAppTenantList(code)

    console.log('=== 租户列表接口返回 ===')
    console.log('完整响应:', JSON.stringify(res, null, 2))
    console.log('========================')

    if (res.code === 200 && res.data) {
      tenantList.value = res.data
      console.log('保存的租户列表:', res.data)

      if (res.data.length === 1) {
        // 只有一个租户，自动选择
        loginForm.value.tenantId = res.data[0].tenantId
        console.log('自动选择租户:', res.data[0])
        uni.showModal({
          title: '租户选择',
          content: `已自动选择租户: ${res.data[0].companyName}`,
          showCancel: false,
        })
      } else if (res.data.length > 1) {
        // 多个租户，显示选择器
        showTenantSelector.value = true
        uni.showToast({
          title: '请选择租户',
          icon: 'none',
        })
      } else {
        uni.showToast({
          title: '未找到可用租户',
          icon: 'none',
        })
      }
    } else {
      console.error('获取租户列表失败:', res)
      uni.showToast({
        title: res.msg || '获取租户列表失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('获取租户列表异常:', error)
    uni.showToast({
      title: '获取租户列表失败',
      icon: 'none',
    })
  }
}

/**
 * 选择租户
 */
const selectTenant = (tenant) => {
  loginForm.value.tenantId = tenant.tenantId
  showTenantSelector.value = false
  console.log('选择租户:', tenant)
  uni.showToast({
    title: `已选择: ${tenant.companyName}`,
    icon: 'success',
  })
}

/**
 * 查看租户列表信息
 */
const showTenantListInfo = async () => {
  if (!loginForm.value.xcxCode) {
    uni.showModal({
      title: '提示',
      content: '请先获取小程序Code',
      showCancel: false,
    })
    return
  }

  try {
    // 重新获取code来获取租户列表，避免code被消耗
    const code = await getXcxCode()
    console.log('获取租户列表用的code:', code)
    await getTenantList(code)

    // 强制显示租户选择器，让用户可以查看和重新选择
    if (tenantList.value.length > 0) {
      showTenantSelector.value = true
    } else {
      uni.showModal({
        title: '提示',
        content: '未找到可用的租户信息',
        showCancel: false,
      })
    }
  } catch (error) {
    console.error('获取租户列表失败:', error)
    uni.showModal({
      title: '错误',
      content: '获取租户列表失败，请检查网络连接后重试',
      showCancel: false,
    })
  }
}

/**
 * 处理登录
 */
const handleLogin = async () => {
  // 验证是否已选择租户
  if (!loginForm.value.tenantId) {
    uni.showToast({
      title: '请先选择租户',
      icon: 'none',
    })
    return
  }

  loading.value = true

  try {
    // 重新获取code用于登录，确保code的有效性
    const code = await getXcxCode()
    console.log('登录用的code:', code)

    // 更新登录表单中的code
    loginForm.value.xcxCode = code

    // Console输出登录参数用于调试
    console.log('=== 登录参数 ===')
    console.log('clientId:', loginForm.value.clientId)
    console.log('grantType:', loginForm.value.grantType)
    console.log('tenantId:', loginForm.value.tenantId)
    console.log('xcxCode:', loginForm.value.xcxCode)
    console.log('===============')

    // 调用登录接口
    await userStore.login(loginForm.value)

    uni.showToast({
      title: '登录成功',
      icon: 'success',
    })

    // 登录成功后跳转到首页
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/cardList/cardList',
      })
    }, 1500)
  } catch (error) {
    console.error('登录失败:', error)
    // 重新获取小程序code
    loginForm.value.xcxCode = ''

    if (error.message && error.message.includes('code')) {
      uni.showToast({
        title: '获取Code失败',
        icon: 'none',
      })
    }
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0

  try {
    // 先获取小程序code
    const code = await getXcxCode()
    // resolved 获得code后调用getTenantList
    await getTenantList(code)
  } catch (error) {
    console.error('初始化失败:', error)
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  padding: 80rpx 0 60rpx;

  .title {
    font-size: 48rpx;
    font-weight: bold;
    color: #fff;
    margin-bottom: 20rpx;
  }

  .subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.login-form {
  flex: 1;
  padding: 60rpx 60rpx 0;
  background: #fff;
  border-radius: 40rpx 40rpx 0 0;

  .login-info {
    background: #f8f9fa;
    border-radius: 16rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 28rpx;
        color: #666;
        font-weight: 500;
      }

      .value {
        font-size: 28rpx;
        color: #333;
        font-weight: 600;
        max-width: 300rpx;
        text-align: right;
        word-break: break-all;
      }
    }
  }

  .form-item {
    margin-bottom: 40rpx;
  }

  .tenant-selector {
    background: #f8f9fa;
    border-radius: 16rpx;
    padding: 30rpx;
    border: 2rpx solid #e9ecef;

    .selector-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 20rpx;
      text-align: center;
    }

    .tenant-list {
      .tenant-item {
        background: #fff;
        border-radius: 12rpx;
        padding: 24rpx;
        margin-bottom: 16rpx;
        border: 2rpx solid #e9ecef;
        transition: all 0.3s ease;

        &:last-child {
          margin-bottom: 0;
        }

        &.active {
          border-color: #007aff;
          background: #f0f8ff;
        }

        .tenant-name {
          font-size: 30rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 8rpx;
        }

        .tenant-id {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
  }
}

.footer {
  padding: 40rpx;
  text-align: center;

  text {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.6);
  }
}
</style>
