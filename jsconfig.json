{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/utils/*": ["./utils/*"], "@/api/*": ["./api/*"], "@/pages/*": ["./pages/*"], "@/static/*": ["./static/*"], "@/store/*": ["./store/*"]}, "target": "es2017", "module": "esnext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": false, "jsx": "preserve", "lib": ["es2017", "dom", "dom.iterable"]}, "include": ["**/*.js", "**/*.vue", "**/*.json"], "exclude": ["node_modules", "uni_modules", "unpackage", "dist"]}