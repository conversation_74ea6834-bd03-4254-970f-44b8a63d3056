@import './parameters.scss';
/* 防止图片闪一下 */
image {
  will-change: transform;
}
body {
  --padbase: 24rpx;
  --marbase: 24rpx;
}
/*盒子 尺寸计算方式*/
.box-border {
  box-sizing: border-box;
}
/* scroll-view */
.scroll-row {
  width: 100%;
  white-space: nowrap;
}
.scroll-row-item {
  display: inline-block;
}

/* 阴影 */
.shadow-xs {
  box-shadow: 0px 1px 0px 0px #e6e6e6;
}
.shadow {
  box-shadow: 0rpx 4rpx 10rpx -2rpx rgba(0, 0, 0, 0.22);
}
.shadow-sm {
  box-shadow:
    0rpx 4rpx 8rpx -2rpx rgba(0, 0, 0, 0.12),
    0rpx 8rpx 10rpx 0rpx rgba(0, 0, 0, 0.08),
    0rpx 2rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
}
.shadow-md {
  box-shadow:
    0rpx 10rpx 10rpx -3rpx rgba(0, 0, 0, 0.1),
    0rpx 16rpx 20rpx 2rpx rgba(0, 0, 0, 0.06),
    0rpx 6rpx 28rpx 4rpx rgba(0, 0, 0, 0.05);
}
.shadow-lg {
  box-shadow:
    0rpx 16rpx 20rpx -10rpx rgba(0, 0, 0, 0.08),
    0rpx 32rpx 48rpx 4rpx rgba(0, 0, 0, 0.04),
    0rpx 12rpx 60rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 定位 */
.position-absolute {
  position: absolute;
}
.position-fixed {
  position: fixed;
}
.position-relative {
  position: relative;
}
.position-sticky {
  position: sticky;
  position: -webkit-sticky;
}
.left-0 {
  left: 0;
}
.top-0 {
  top: 0;
}
.right-0 {
  right: 0;
}
.bottom-0 {
  bottom: 0;
}
.position-fixed-bt {
  left: 0;
  right: 0;
  bottom: 0;
}
/* 宽高 */
.width-100 {
  width: 100%;
}
.width-0 {
  width: 0;
}
.mwidth-100 {
  min-width: 100%;
}
.mwidth-0 {
  min-width: 0;
}
.w-100 {
  width: 750rpx;
}
.width-auto {
  width: auto;
}
.width-50 {
  width: 50%;
}

.height-auto {
  height: auto;
}
.height-100 {
  height: 100%;
}
.height-50 {
  height: 50%;
}
.height-100vh {
  height: 100vh;
}
.height-50vh {
  height: 50vh;
}

.mheight-100 {
  min-height: 100%;
}
.mheight-100vh {
  min-height: 100vh;
}
.mheight-inherit {
  min-height: inherit;
}
.mheight-demo {
  min-height: 24rpx;
}

/* 字体 */

.font-xxxs,
.font-12 {
  font-size: 24rpx;
}
.font-xxs,
.font-13 {
  font-size: 26rpx;
}
.font-xs,
.font-14 {
  font-size: 28rpx;
}
.font-s,
.font-15 {
  font-size: 30rpx;
}
.font,
.font-16 {
  font-size: 32rpx;
}
.font-m,
.font-17 {
  font-size: 34rpx;
}
.font-xm,
.font-18 {
  font-size: 36rpx;
}
.font-19 {
  font-size: 38rpx;
}
.font-xxm,
.font-20 {
  font-size: 40rpx;
}

.font-weight-normal {
  font-weight: normal;
}
.font-weight-bold {
  font-weight: bold;
}
.font-weight-100 {
  font-weight: 100;
}
.font-weight-200 {
  font-weight: 200;
}
.font-weight-500 {
  font-weight: 500;
}

.font-inherit {
  font-size: inherit;
}

/*行高*/
.line-h0 {
  line-height: 0;
}
.line-h {
  line-height: 1;
}
.line-h-sm {
  line-height: 1.2;
}
.line-h-md {
  line-height: 1.5;
}
.line-h-mmd {
  line-height: 2;
}
.line-h-lg {
  line-height: 3;
}

/* 文字划线 */
.text-through {
  text-decoration: line-through;
}
.text-underline {
  text-decoration: underline;
}
.text-hover-underline:hover {
  text-decoration: underline;
}

/* 文字对齐 */
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-left {
  text-align: left;
}
.text-justify {
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
  white-space: pre-line; /*兼容ios*/
  word-break: break-word;

  &.ios:after {
    display: inline-block;
    content: '';
    overflow: hidden;
    width: 100%;
    height: 0;
  }
}

.row {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
[class*='col-'],
[class*='span-'],
[class*='span24-'] {
  min-height: 1rpx;
  box-sizing: border-box;
}

/* 栅栏一  分成12分 每份是8.3%*/
.col-1 {
  width: 62.5rpx;
}
.col-2 {
  width: 125rpx;
}
.col-3 {
  width: 187.5rpx;
}
.col-4 {
  width: 250rpx;
}
.col-5 {
  width: 312.5rpx;
}
.col-6 {
  width: 375rpx;
}
.col-7 {
  width: 437.5rpx;
}
.col-8 {
  width: 500rpx;
}
.col-9 {
  width: 562.5rpx;
}
.col-10 {
  width: 625rpx;
}
.col-11 {
  width: 687.5rpx;
}
.col-12 {
  width: 750rpx;
}

/* 栅栏二 分成20分 每份是5% */
.span-1 {
  width: 5%;
}
.span-2 {
  width: 10%;
}
.span-3 {
  width: 15%;
}
.span-4 {
  width: 20%;
}
.span-5 {
  width: 25%;
}
.span-6 {
  width: 30%;
}
.span-7 {
  width: 35%;
}
.span-8 {
  width: 40%;
}
.span-9 {
  width: 45%;
}
.span-10 {
  width: 50%;
}
.span-11 {
  width: 55%;
}
.span-12 {
  width: 60%;
}
.span-13 {
  width: 65%;
}
.span-14 {
  width: 70%;
}
.span-15 {
  width: 75%;
}
.span-16 {
  width: 80%;
}
.span-17 {
  width: 85%;
}
.span-18 {
  width: 90%;
}
.span-19 {
  width: 95%;
}
.span-20 {
  width: 100%;
}

/* 栅栏三 */
.span24-1 {
  width: 4.17%;
}
.span24-2 {
  width: 8.33%;
}
.span24-3 {
  width: 12.5%;
}
.span24-4 {
  width: 16.67%;
}
.span24-5 {
  width: 20.83%;
}
.span24-6 {
  width: 25%;
}
.span24-7 {
  width: 29.17%;
}
.span24-8 {
  width: 33.33%;
}
.span24-9 {
  width: 37.5%;
}
.span24-10 {
  width: 41.67%;
}
.span24-11 {
  width: 45.83%;
}
.span24-12 {
  width: 50%;
}
.span24-13 {
  width: 54.17%;
}
.span24-14 {
  width: 58.33%;
}
.span24-15 {
  width: 62.5%;
}
.span24-16 {
  width: 66.67%;
}
.span24-17 {
  width: 70.83%;
}
.span24-18 {
  width: 75%;
}
.span24-19 {
  width: 79.17%;
}
.span24-20 {
  width: 83.33%;
}
.span24-21 {
  width: 87.5%;
}
.span24-22 {
  width: 91.67%;
}
.span24-23 {
  width: 95.83%;
}
.span24-24 {
  width: 100%;
}

/* flex布局 */
.d-flex {
  display: flex;
}
.d-inline-block {
  display: inline-block;
}
.d-block {
  display: block;
}

/* flex 布局 */
.flex {
  display: flex;
  flex-direction: row;
}
.flex-row {
  flex-direction: row;
}
.flex-column {
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.flex-row-reverse {
  flex-direction: row-reverse;
}
.flex-column-reverse {
  flex-direction: column-reverse;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-wrap-reverse {
  flex-wrap: wrap-reverse;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-between {
  justify-content: space-between;
}
.justify-center {
  justify-content: center;
}
.justify-around {
  justify-content: space-around;
}
.justify-evenly {
  justify-content: space-evenly;
}
.align-center {
  align-items: center;
}
.align-stretch {
  align-items: stretch;
}
.align-start {
  align-items: flex-start;
}
.align-end {
  align-items: flex-end;
}
.align-self-end {
  align-self: flex-end;
}
.align-self-center {
  align-self: flex-center;
}
.align-self-start {
  align-self: flex-start;
}

.content-start {
  align-content: flex-start;
}
.content-end {
  align-content: flex-end;
}
.content-center {
  align-content: center;
}
.content-between {
  align-content: space-between;
}
.content-around {
  align-content: space-around;
}
.content-stretch {
  align-content: stretch;
}
.content-evenly {
  align-content: space-evenly;
}

.flex-1 {
  flex: 1;
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0;
}
.flex-2 {
  flex: 2;
}
.flex-3 {
  flex: 3;
}
.flex-4 {
  flex: 4;
}
.flex-5 {
  flex: 5;
}
.flex-shrink {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}

/* border */
.border-red {
  border: 1rpx solid red;
} /*调试*/
.border {
  border-width: 1rpx;
  border-style: solid;
  border-color: $borderColor;
}
.border-current {
  border-width: 1rpx;
  border-style: solid;
  border-color: currentColor;
}
.border-top {
  border: 0;
  border-top-width: 1rpx;
  border-top-style: solid;
  border-color: $borderColor;
}
.border-right {
  border: 0;
  border-right-width: 1rpx;
  border-right-style: solid;
  border-color: $borderColor;
}
.border-bottom {
  border: 0;
  border-bottom-width: 1rpx;
  border-bottom-style: solid;
  border-color: $borderColor;
}
.border-left {
  border: 0;
  border-left-width: 1rpx;
  border-left-style: solid;
  border-color: $borderColor;
}

.border-0 {
  border-width: 0;
}
.border-top-0 {
  border-top-width: 0;
}
.border-right-0 {
  border-right-width: 0;
}
.border-bottom-0 {
  border-bottom-width: 0;
}
.border-left-0 {
  border-left-width: 0;
}

.border-primary {
  border-color: $primary;
}
.border-primary-muted {
  border-color: $primary-muted;
}
.border-primary-light {
  border-color: $primary-light;
}

.border-success {
  border-color: $success;
}
.border-success-muted {
  border-color: $success-muted;
}
.border-success-light {
  border-color: $success-light;
}
.border-danger {
  border-color: $danger;
}
.border-info {
  border-color: $info;
}
.border-warning {
  border-color: $warning;
}
.border-warning-muted {
  border-color: $warning-muted;
}
.border-warning-light {
  border-color: $warning-light;
}
.border-light {
  border-color: $light;
}
.border-light-detail {
  border-color: $light-detail;
}
.border-theme-brand {
  border-color: $themeBrand;
}

.border-first {
  border-color: $first;
}
.border-second {
  border-color: $second;
}
.border-third {
  border-color: $third;
}
.border-fourth {
  border-color: $fourth;
}
.border-white {
  border-color: $white;
}

.border-red {
  border: 1rpx solid red;
}
.border-dashed {
  border-style: dashed;
}
.border-dotted {
  border-style: dotted;
}
/*圆角*/
.rounded-6 {
  border-radius: 12rpx;
}
.rounded-8 {
  border-radius: 16rpx;
}
.rounded-12 {
  border-radius: 24rpx;
}
.rounded-16 {
  border-radius: 32rpx;
}
.rounded-20 {
  border-radius: 40rpx;
}
.rounded-40 {
  border-radius: 80rpx;
}
.rounded-80 {
  border-radius: 160rpx;
}
.rounded-circle {
  border-radius: 100%;
}
.rounded-0 {
  border-radius: 0;
}
.rounded-top-left-0 {
  border-top-left-radius: 0;
}
.rounded-top-right-0 {
  border-top-right-radius: 0;
}
.rounded-bottom-left-0 {
  border-bottom-left-radius: 0;
}
.rounded-bottom-right-0 {
  border-bottom-right-radius: 0;
}

/* color */
/*文字颜色*/
.text-primary {
  color: $primary;
}
.text-primary-muted {
  color: $primary-muted;
}
.text-primary-light {
  color: $primary-light;
}

.text-success {
  color: $success;
}
.text-success-muted {
  color: $success-muted;
}
.text-success-light {
  color: $success-light;
}
.text-danger {
  color: $danger;
}
.text-danger-muted {
  color: $danger-muted;
}
.text-danger-light {
  color: $danger-light;
}

.text-info {
  color: $info;
}
.text-warning {
  color: $warning;
}
.text-warning-muted {
  color: $warning-muted;
}
.text-warning-light {
  color: $warning-light;
}
.text-light {
  color: $light;
}

.text-first {
  color: $first;
}
.text-second {
  color: $second;
}
.text-third {
  color: $third;
}
.text-fourth {
  color: $fourth;
}
.text-placeholder {
  color: $placeholder;
}
.text-colorInput {
  color: $colorInput;
}
.text-light-muted {
  color: $light-muted;
}

.text-theme-brand {
  color: $themeBrand;
}

.text-white {
  color: $white;
}
.text-black {
  color: $black;
}
.text-red {
  color: $red;
}
.text-placeholder {
  color: $uni-text-color-placeholder;
}
/*字体间距*/
.letter-spacing-2 {
  letter-spacing: 4rpx;
}
.letter-spacing-4 {
  letter-spacing: 8rpx;
}
.letter-spacing-6 {
  letter-spacing: 12rpx;
}
.letter-spacing-8 {
  letter-spacing: 16rpx;
}
/*首行缩进*/
.text-indent-4 {
  text-indent: 8rpx;
}
.text-indent-6 {
  text-indent: 12rpx;
}
/*背景颜色*/

.bg-white {
  background-color: $white;
}
.bg-black {
  background-color: $black;
}
.bg-red {
  background-color: $red;
}
.bg-transparent {
  background-color: transparent;
}
.bg-primary {
  background-color: $primary;
}
.bg-primary-muted {
  background-color: $primary-muted;
}
.bg-primary-light {
  background-color: $primary-light;
}
.bg-success {
  background-color: $success;
}
.bg-success-muted {
  background-color: $success-muted;
}
.bg-success-light {
  background-color: $success-light;
}
.bg-danger {
  background-color: $danger;
}
.bg-danger-muted {
  background-color: $danger-muted;
}
.bg-danger-light {
  background-color: $danger-light;
}
.bg-warning {
  background-color: $warning;
}
.bg-warning-muted {
  background-color: $warning-muted;
}
.bg-warning-light {
  background-color: $warning-light;
}
.bg-info {
  background-color: $info;
}
.bg-light {
  background-color: $light;
}
.bg-light-detail {
  background-color: $light-detail;
}
.bg-light-muted {
  background-color: $light-muted;
}

.bg-first {
  background-color: $first;
}
.bg-second {
  background-color: $second;
}
.bg-third {
  background-color: $third;
}
.bg-fourth {
  background-color: $fourth;
}

.bg-theme-brand {
  background-color: $themeBrand;
}
.bg-hover {
  background-color: $hoverPrimary;
  box-shadow: 0px 0px 10rpx rgba(#000, 0.3);
}
.bg-hover-light {
  background-color: $light;
}
/*form*/
.bg-input {
  background-color: $bgInput;
}

.gradient-black {
  background: linear-gradient(90deg, #505050 0%, #505050 0%, #303030 100%, #303030 100%);
}
.gradient-warning {
  background: linear-gradient(90deg, #fae9cd 0%, #fae9cd 0%, #f5d39f 100%, #f5d39f 100%);
}

/* Spacing */
.m-0 {
  margin-left: 0;
  margin-right: 0;
  margin-top: 0;
  margin-bottom: 0;
}
.m-base {
  margin: $marbase;
}
.m {
  margin-left: 5rpx;
  margin-right: 5rpx;
  margin-top: 5rpx;
  margin-bottom: 5rpx;
}
.m-1 {
  margin-left: 10rpx;
  margin-right: 10rpx;
  margin-top: 10rpx;
  margin-bottom: 10rpx;
}
.m-0-8 {
  margin-left: 16rpx;
  margin-right: 16rpx;
  margin-top: 16rpx;
  margin-bottom: 16rpx;
}
.m-2 {
  margin-left: 20rpx;
  margin-right: 20rpx;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}
.m-1-2 {
  margin-left: 24rpx;
  margin-right: 24rpx;
  margin-top: 24rpx;
  margin-bottom: 24rpx;
}
.m-3 {
  margin-left: 32rpx;
  margin-right: 32rpx;
  margin-top: 32rpx;
  margin-bottom: 32rpx;
}
.m-4 {
  margin-left: 40rpx;
  margin-right: 40rpx;
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}
.m-5 {
  margin-left: 50rpx;
  margin-right: 50rpx;
  margin-top: 50rpx;
  margin-bottom: 50rpx;
}

.mx-0 {
  margin-left: 0;
  margin-right: 0;
}
.mx-base {
  margin-left: $marbase;
  margin-right: $marbase;
}
.mx {
  margin-left: 5rpx;
  margin-right: 5rpx;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.mx-1 {
  margin-left: 10rpx;
  margin-right: 10rpx;
}
.mx-0-8 {
  margin-left: 16rpx;
  margin-right: 16rpx;
}
.mx-2 {
  margin-left: 20rpx;
  margin-right: 20rpx;
}
.mx-1-2 {
  margin-left: 24rpx;
  margin-right: 24rpx;
}
.mx-3 {
  margin-left: 32rpx;
  margin-right: 32rpx;
}
.mx-4 {
  margin-left: 40rpx;
  margin-right: 40rpx;
}
.mx-5 {
  margin-left: 50rpx;
  margin-right: 50rpx;
}

.my-0 {
  margin-top: 0;
  margin-bottom: 0;
}
.my-base {
  margin-top: $marbase;
  margin-bottom: $marbase;
}
.my {
  margin-top: 5rpx;
  margin-bottom: 5rpx;
}
.my-1 {
  margin-top: 10rpx;
  margin-bottom: 10rpx;
}
.my-0-8 {
  margin-top: 16rpx;
  margin-bottom: 16rpx;
}
.my-2 {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}
.my-1-2 {
  margin-top: 24rpx;
  margin-bottom: 24rpx;
}
.my-3 {
  margin-top: 32rpx;
  margin-bottom: 32rpx;
}
.my-4 {
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}
.my-5 {
  margin-top: 50rpx;
  margin-bottom: 50rpx;
}

.mt-0 {
  margin-top: 0;
}
.mt-base {
  margin-top: $marbase;
}
.mt {
  margin-top: 5rpx;
}
.mt-auto {
  margin-top: auto;
}
.mt-1 {
  margin-top: 10rpx;
}
.mt-0-8 {
  margin-top: 16rpx;
}
.mt-2 {
  margin-top: 20rpx;
}
.mt-1-2 {
  margin-top: 24rpx;
}
.mt-3 {
  margin-top: 32rpx;
}
.mt-4 {
  margin-top: 40rpx;
}
.mt-5 {
  margin-top: 50rpx;
}

.mb {
  margin-bottom: 5rpx;
}
.mb-base {
  margin-bottom: $marbase;
}
.mb-auto {
  margin-bottom: auto;
}
.mb-1 {
  margin-bottom: 10rpx;
}
.mb-0-8 {
  margin-bottom: 16rpx;
}
.mb-2 {
  margin-bottom: 20rpx;
}
.mb-1-2 {
  margin-bottom: 24rpx;
}
.mb-3 {
  margin-bottom: 32rpx;
}
.mb-4 {
  margin-bottom: 40rpx;
}
.mb-5 {
  margin-bottom: 50rpx;
}
.mb-0 {
  margin-bottom: 0;
}

.ml-0 {
  margin-left: 0;
}
.ml-base {
  margin-left: $marbase;
}
.ml {
  margin-left: 5rpx;
}
.ml-auto {
  margin-left: auto;
}
.ml-1 {
  margin-left: 10rpx;
}
.ml-0-8 {
  margin-left: 16rpx;
}
.ml-2 {
  margin-left: 20rpx;
}
.ml-1-2 {
  margin-left: 24rpx;
}
.ml-3 {
  margin-left: 32rpx;
}
.ml-4 {
  margin-left: 40rpx;
}
.ml-5 {
  margin-left: 50rpx;
}
.ml-100- {
  margin-left: -100%;
}

.mr-0 {
  margin-right: 0;
}
.mr-base {
  margin-right: $marbase;
}
.mr {
  margin-right: 5rpx;
}
.mr-1 {
  margin-right: 10rpx;
}
.mr-0-8 {
  margin-right: 16rpx;
}
.mr-2 {
  margin-right: 20rpx;
}
.mr-1-2 {
  margin-right: 24rpx;
}
.mr-3 {
  margin-right: 32rpx;
}
.mr-4 {
  margin-right: 40rpx;
}
.mr-5 {
  margin-right: 50rpx;
}

.p-0 {
  padding-left: 0;
  padding-right: 0;
  padding-top: 0;
  padding-bottom: 0;
}
.p-base {
  padding: $padbase;
}
.p {
  padding-left: 5rpx;
  padding-right: 5rpx;
  padding-top: 5rpx;
  padding-bottom: 5rpx;
}
.p-1 {
  padding-left: 10rpx;
  padding-right: 10rpx;
  padding-top: 10rpx;
  padding-bottom: 10rpx;
}
.p-0-8 {
  padding-left: 16rpx;
  padding-right: 16rpx;
  padding-top: 16rpx;
  padding-bottom: 16rpx;
}
.p-2 {
  padding-left: 20rpx;
  padding-right: 20rpx;
  padding-top: 20rpx;
  padding-bottom: 20rpx;
}
.p-1-2 {
  padding-left: 24rpx;
  padding-right: 24rpx;
  padding-top: 24rpx;
  padding-bottom: 24rpx;
}
.p-3 {
  padding-left: 32rpx;
  padding-right: 32rpx;
  padding-top: 32rpx;
  padding-bottom: 32rpx;
}
.p-4 {
  padding-left: 40rpx;
  padding-right: 40rpx;
  padding-top: 40rpx;
  padding-bottom: 40rpx;
}
.p-5 {
  padding-left: 50rpx;
  padding-right: 50rpx;
  padding-top: 50rpx;
  padding-bottom: 50rpx;
}

.px-0 {
  padding-left: 0;
  padding-right: 0;
}
.px-base {
  padding-left: $padbase;
  padding-right: $padbase;
}
.px {
  padding-left: 5rpx;
  padding-right: 5rpx;
}
.px-1 {
  padding-left: 10rpx;
  padding-right: 10rpx;
}
.px-0-8 {
  padding-left: 16rpx;
  padding-right: 16rpx;
}
.px-2 {
  padding-left: 20rpx;
  padding-right: 20rpx;
}
.px-1-2 {
  padding-left: 24rpx;
  padding-right: 24rpx;
}
.px-3 {
  padding-left: 32rpx;
  padding-right: 32rpx;
}
.px-4 {
  padding-left: 40rpx;
  padding-right: 40rpx;
}
.px-5 {
  padding-left: 50rpx;
  padding-right: 50rpx;
}

.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}
.py-base {
  padding-top: $padbase;
  padding-bottom: $padbase;
}
.py {
  padding-top: 5rpx;
  padding-bottom: 5rpx;
}
.py-1 {
  padding-top: 10rpx;
  padding-bottom: 10rpx;
}
.py-0-8 {
  padding-top: 16rpx;
  padding-bottom: 16rpx;
}
.py-2 {
  padding-top: 20rpx;
  padding-bottom: 20rpx;
}
.py-1-2 {
  padding-top: 24rpx;
  padding-bottom: 24rpx;
}
.py-3 {
  padding-top: 32rpx;
  padding-bottom: 32rpx;
}
.py-4 {
  padding-top: 40rpx;
  padding-bottom: 40rpx;
}
.py-5 {
  padding-top: 50rpx;
  padding-bottom: 50rpx;
}

.pt-0 {
  padding-top: 0;
}
.pt-base {
  padding-top: $padbase;
}
.pt {
  padding-top: 5rpx;
}
.pt-1 {
  padding-top: 10rpx;
}
.pt-0-8 {
  padding-top: 16rpx;
}
.pt-2 {
  padding-top: 20rpx;
}
.pt-1-2 {
  padding-top: 24rpx;
}
.pt-3 {
  padding-top: 32rpx;
}
.pt-4 {
  padding-top: 40rpx;
}
.pt-5 {
  padding-top: 50rpx;
}

.pb-0 {
  padding-bottom: 0;
}
.pb-base {
  padding-bottom: $padbase;
}
.pb {
  padding-bottom: 5rpx;
}
.pb-1 {
  padding-bottom: 10rpx;
}
.pb-0-8 {
  padding-bottom: 16rpx;
}
.pb-2 {
  padding-bottom: 20rpx;
}
.pb-1-2 {
  padding-bottom: 24rpx;
}
.pb-3 {
  padding-bottom: 32rpx;
}
.pb-4 {
  padding-bottom: 40rpx;
}
.pb-5 {
  padding-bottom: 50rpx;
}

.pl-0 {
  padding-left: 0;
}
.pl-base {
  padding-left: $padbase;
}
.pl {
  padding-left: 5rpx;
}
.pl-1 {
  padding-left: 10rpx;
}
.pl-0-8 {
  padding-left: 16rpx;
}
.pl-2 {
  padding-left: 20rpx;
}
.pl-1-2 {
  padding-left: 24rpx;
}
.pl-3 {
  padding-left: 32rpx;
}
.pl-4 {
  padding-left: 40rpx;
}
.pl-5 {
  padding-left: 50rpx;
}

.pr-0 {
  padding-right: 0;
}
.pr-base {
  padding-right: $padbase;
}
.pr {
  padding-right: 5rpx;
}
.pr-1 {
  padding-right: 10rpx;
}
.pr-0-8 {
  padding-right: 16rpx;
}
.pr-2 {
  padding-right: 20rpx;
}
.pr-1-2 {
  padding-right: 24rpx;
}
.pr-3 {
  padding-right: 32rpx;
}
.pr-4 {
  padding-right: 40rpx;
}
.pr-5 {
  padding-right: 50rpx;
}

/* 文字划线 */
.text-through {
  text-decoration: line-through;
}
.text-underline {
  text-decoration: underline;
}
.text-hover-underline:hover {
  text-decoration: underline;
}
/* 鼠标 */
.cursor-pointer {
  cursor: pointer;
}
.cursor-default {
  cursor: default;
}
.cursor-auto {
  cursor: auto;
}
/* 多行文本隐藏 */
.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.text-overflow {
  overflow: hidden;
  white-space: nowrap;
}
.word-break {
  word-wrap: break-word;
  word-break: break-all;
}
/* 内容溢出 */
.overflow-hidden {
  overflow: hidden;
}
.overflow-auto {
  overflow: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-hidden {
  overflow-y: hidden;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.overflow-visible {
  overflow: visible;
}
/* 垂直对齐 */
.vertical-middle {
  vertical-align: middle;
}
.vertical-baseline {
  vertical-align: baseline;
}
.vertical-top {
  vertical-align: text-top;
}
.vertical-bottom {
  vertical-align: text-bottom;
}
/*层级*/
.zindex-1- {
  z-index: -1;
}
.zindex-1 {
  z-index: 1;
}
.zindex-2 {
  z-index: 2;
}
.zindex-3 {
  z-index: 3;
}
.zindex-4 {
  z-index: 4;
}
.zindex-5 {
  z-index: 5;
}
.zindex-6 {
  z-index: 6;
}
.zindex-7 {
  z-index: 7;
}
.zindex-8 {
  z-index: 8;
}
.zindex-9 {
  z-index: 9;
}
.zindex-10 {
  z-index: 10;
}
.zindex-55 {
  z-index: 55;
}
.zindex-99 {
  z-index: 99;
}
.zindex-999 {
  z-index: 999;
}
/*缩放*/
.scale-primary {
  transform: scale(0.7);
}
.scale-70 {
  transform: scale(0.7);
}
.scale-80 {
  transform: scale(0.8);
}
.scale-90 {
  transform: scale(0.9);
}
/*透明度*/

.opacity-1 {
  opacity: 1;
}
.opacity-0_5 {
  opacity: 0.5;
}
.opacity-0 {
  opacity: 0;
  filter: alpha(opacity=0);
}
/**/
.no-scrollbar {
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
    background: transparent;
  }
}
