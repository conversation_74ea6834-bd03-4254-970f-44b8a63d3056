<template>
  <view class="navigationBar" :class="classname">
    <view class="status_bar"></view>
    <view class="position-relative py-1 main">
      <view v-if="showBack" class="back-box" @tap="">
        <image src="/static/images/back.png" mode="aspectFit" class="width-100 height-100"></image>
      </view>
      <view class="flex justify-center text-first title">{{ title }}</view>
    </view>
  </view>
</template>

<script setup>
import { defineProps } from 'vue'
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  showBack: {
    type: Boolean,
    default: true,
  },
  classname: {
    type: String,
    default: '',
  },
})
function onGoback() {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.navigationBar {
  .title {
    margin-top: 16rpx;
  }
  .main {
    padding: 16rpx 80rpx;
    position: relative;
  }
  .status_bar {
    height: var(--status-bar-height);
    width: 100%;
  }
  .back-box {
    position: absolute;
    width: 40rpx;
    height: 40rpx;
    left: 40rpx;
    border: 1px solid red;
  }
}
</style>
