@import '../common/abstracts/variable';
@import '../common/abstracts/mixin';

.wot-theme-dark {
  @include b(table) {
    background: $-dark-background;

    @include when(border) {
      border: 1px solid $-dark-border-color;
    }

    @include e(cell) {
      color: $-dark-color;
      background: $-dark-background2;

      @include when(stripe) {
        background: $-dark-background4;
      }

      @include when(border) {
        border-right: 1px solid $-dark-border-color;
        border-bottom: 1px solid $-dark-border-color;
      }

      @include when(shadow) {
        &::after {
          background: linear-gradient(270deg, rgba(17, 17, 17, 0.2) 0%, rgba(0, 0, 0, 0) 100%);
        }
      }
    }
  }
}



@include b(table) {
  position: relative;
  width: 100%;
  overflow: auto;
  background: $-table-bg;

  @include when(border) {
    border: 1px solid $-table-border-color;
  }

  :deep() {
    .wd-table-col:last-child {
      .wd-table__cell {
        @include when(border) {
          border-right: none;
        }
      }
    }
  }

  @include e(header) {
    width: 100%;
    height: 50px;
    position: sticky;
    top: 0;
    z-index: 2;
    display: flex;
    overflow-x: auto;
    white-space: nowrap;
  }

  @include e(body) {
    width: 100%;
    box-sizing: border-box;
  }

  @include e(content) {
    display: flex;

    @include m(header) {
      position: sticky;
      top: 0;
      z-index: 2
    }
  }

  @include e(cell) {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    background: $-table-bg;
    width: 100px;
    min-height: 50px;
    padding: 8px 10px;
    font-size: $-table-font-size;
    color: $-table-color;
    --wot-sort-button-height: 30px;

    @include when(border) {
      border-right: 1px solid $-table-border-color;
      border-bottom: 1px solid $-table-border-color;
    }

    @include when(stripe) {
      background: $-table-stripe-bg;
    }

    @include when(fixed) {
      position: sticky;
      z-index: 1;
      left: 0;
    }

    @include when(shadow) {
      &::after {
        content: ' ';
        position: absolute;
        height: 100%;
        right: -30rpx;
        top: 0;
        width: 30rpx;
        height: 100%;
        background: linear-gradient(270deg, rgba(255, 255, 255, 0) 0%, rgba(0, 0, 0, 0.04) 100%);
      }
    }

    @include when(left) {
      justify-content: flex-start;
    }

    @include when(center) {
      justify-content: center;
    }

    @include when(right) {
      justify-content: flex-end;
    }
  }

  @include e(wrapper) {
    width: 100%;
    overflow: auto;
  }

  @include e(inner) {
    display: flex;
    flex-direction: column;
  }

  @include e(header-row) {
    display: flex;
    flex-direction: row;
    width: 100%;
  }

  @include e(body) {
    display: flex;
    width: 100%;
  }

  @include e(value) {
    @include when(ellipsis) {
      word-break: break-all;
      @include multiEllipsis(2);
    }
  }
}