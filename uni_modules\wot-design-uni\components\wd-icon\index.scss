@import '../common/abstracts/variable';
@import '../common/abstracts/mixin';

@font-face {
  font-family: 'wd-icons';
  src: url('https://at.alicdn.com/t/c/font_4245058_s5cpwl25n7o.woff2?t=1696817709651') format('woff2'),
    url('https://at.alicdn.com/t/c/font_4245058_s5cpwl25n7o.woff?t=1696817709651') format('woff'),
    url('https://at.alicdn.com/t/c/font_4245058_s5cpwl25n7o.ttf?t=1696817709651') format('truetype');
  font-weight: normal;
  font-style: normal;
}

/*  #ifdef  APP-PLUS  */
@font-face {
  font-family: 'wd-icons';
  src:
    url('./wd-icons.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

/*  #endif  */

@include b(icon) {
  display: inline-block;
  font-family: 'wd-icons' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  &::before {
    display: inline-block;
  }

  @include m(image) {
    width: 1em;
    height: 1em;
  }

  @include e(image) {
    width: 100%;
    height: 100%;
  }
}



.wd-icon-usergroup-clear:before {
  content: "\e739";
}

.wd-icon-user-circle:before {
  content: "\e73a";
}

.wd-icon-user-talk:before {
  content: "\e73b";
}

.wd-icon-user-clear:before {
  content: "\e73c";
}

.wd-icon-user:before {
  content: "\e73d";
}

.wd-icon-usergroup-add:before {
  content: "\e73e";
}

.wd-icon-usergroup:before {
  content: "\e73f";
}

.wd-icon-user-add:before {
  content: "\e740";
}

.wd-icon-user-avatar:before {
  content: "\e741";
}

.wd-icon-pointing-hand:before {
  content: "\e742";
}

.wd-icon-cursor:before {
  content: "\e743";
}

.wd-icon-fullsreen:before {
  content: "\e72c";
}

.wd-icon-cloud-download:before {
  content: "\e72d";
}

.wd-icon-chevron-down-rectangle:before {
  content: "\e72e";
}

.wd-icon-edit:before {
  content: "\e72f";
}

.wd-icon-fullscreen-exit:before {
  content: "\e730";
}

.wd-icon-circle1:before {
  content: "\e731";
}

.wd-icon-close-normal:before {
  content: "\e732";
}

.wd-icon-browse:before {
  content: "\e733";
}

.wd-icon-browse-off:before {
  content: "\e734";
}

.wd-icon-chevron-up-rectangle:before {
  content: "\e735";
}

.wd-icon-add-rectangle:before {
  content: "\e736";
}

.wd-icon-add1:before {
  content: "\e737";
}

.wd-icon-add-circle1:before {
  content: "\e738";
}

.wd-icon-download1:before {
  content: "\e71c";
}

.wd-icon-link:before {
  content: "\e71d";
}

.wd-icon-edit-1:before {
  content: "\e71e";
}

.wd-icon-jump:before {
  content: "\e71f";
}

.wd-icon-chevron-down-circle:before {
  content: "\e720";
}

.wd-icon-delete1:before {
  content: "\e721";
}

.wd-icon-filter-clear:before {
  content: "\e722";
}

.wd-icon-check-rectangle-filled:before {
  content: "\e723";
}

.wd-icon-minus-circle-filled:before {
  content: "\e724";
}

.wd-icon-play:before {
  content: "\e725";
}

.wd-icon-pause-circle-filled:before {
  content: "\e726";
}

.wd-icon-filter1:before {
  content: "\e727";
}

.wd-icon-move:before {
  content: "\e728";
}

.wd-icon-login:before {
  content: "\e729";
}

.wd-icon-minus-circle:before {
  content: "\e72a";
}

.wd-icon-close-circle:before {
  content: "\e72b";
}

.wd-icon-logout:before {
  content: "\e70b";
}

.wd-icon-search1:before {
  content: "\e70c";
}

.wd-icon-pause-circle:before {
  content: "\e70d";
}

.wd-icon-play-circle:before {
  content: "\e70e";
}

.wd-icon-more1:before {
  content: "\e70f";
}

.wd-icon-minus-rectangle:before {
  content: "\e710";
}

.wd-icon-stop:before {
  content: "\e711";
}

.wd-icon-scan1:before {
  content: "\e712";
}

.wd-icon-close-rectangle:before {
  content: "\e713";
}

.wd-icon-rollback:before {
  content: "\e714";
}

.wd-icon-a-order-adjustmentcolumn:before {
  content: "\e715";
}

.wd-icon-pause:before {
  content: "\e716";
}

.wd-icon-ellipsis:before {
  content: "\e717";
}

.wd-icon-cloud-upload:before {
  content: "\e718";
}

.wd-icon-stop-circle-filled:before {
  content: "\e719";
}

.wd-icon-clear:before {
  content: "\e71a";
}

.wd-icon-remove:before {
  content: "\e71b";
}

.wd-icon-zoom-out:before {
  content: "\e6fb";
}

.wd-icon-thumb-down:before {
  content: "\e6fc";
}

.wd-icon-setting1:before {
  content: "\e6fd";
}

.wd-icon-save:before {
  content: "\e6fe";
}

.wd-icon-unfold-more:before {
  content: "\e6ff";
}

.wd-icon-zoom-in:before {
  content: "\e700";
}

.wd-icon-thumb-up:before {
  content: "\e701";
}

.wd-icon-unfold-less:before {
  content: "\e702";
}

.wd-icon-play-circle-filled:before {
  content: "\e703";
}

.wd-icon-poweroff:before {
  content: "\e704";
}

.wd-icon-share:before {
  content: "\e705";
}

.wd-icon-refresh1:before {
  content: "\e706";
}

.wd-icon-link-unlink:before {
  content: "\e707";
}

.wd-icon-upload:before {
  content: "\e708";
}

.wd-icon-rectangle:before {
  content: "\e709";
}

.wd-icon-stop-circle:before {
  content: "\e70a";
}

.wd-icon-backtop-rectangle:before {
  content: "\e6ea";
}

.wd-icon-caret-down:before {
  content: "\e6eb";
}

.wd-icon-arrow-left1:before {
  content: "\e6ec";
}

.wd-icon-help-circle:before {
  content: "\e6ed";
}

.wd-icon-help-circle-filled:before {
  content: "\e6ee";
}

.wd-icon-time-filled:before {
  content: "\e6ef";
}

.wd-icon-close-circle-filled:before {
  content: "\e6f0";
}

.wd-icon-info-circle:before {
  content: "\e6f1";
}

.wd-icon-info-circle-filled:before {
  content: "\e6f2";
}

.wd-icon-check1:before {
  content: "\e6f3";
}

.wd-icon-help:before {
  content: "\e6f4";
}

.wd-icon-error:before {
  content: "\e6f5";
}

.wd-icon-check-circle:before {
  content: "\e6f6";
}

.wd-icon-error-circle-filled:before {
  content: "\e6f7";
}

.wd-icon-error-circle:before {
  content: "\e6f8";
}

.wd-icon-check-rectangle:before {
  content: "\e6f9";
}

.wd-icon-check-circle-filled:before {
  content: "\e6fa";
}

.wd-icon-chevron-up:before {
  content: "\e6da";
}

.wd-icon-chevron-up-circle:before {
  content: "\e6db";
}

.wd-icon-chevron-right:before {
  content: "\e6dc";
}

.wd-icon-arrow-down-rectangle:before {
  content: "\e6dd";
}

.wd-icon-caret-up-small:before {
  content: "\e6de";
}

.wd-icon-chevron-right-rectangle:before {
  content: "\e6df";
}

.wd-icon-caret-right-small:before {
  content: "\e6e0";
}

.wd-icon-arrow-right1:before {
  content: "\e6e1";
}

.wd-icon-backtop:before {
  content: "\e6e2";
}

.wd-icon-arrow-up1:before {
  content: "\e6e3";
}

.wd-icon-caret-up:before {
  content: "\e6e4";
}

.wd-icon-backward:before {
  content: "\e6e5";
}

.wd-icon-arrow-down1:before {
  content: "\e6e6";
}

.wd-icon-chevron-left:before {
  content: "\e6e7";
}

.wd-icon-caret-right:before {
  content: "\e6e8";
}

.wd-icon-caret-left:before {
  content: "\e6e9";
}

.wd-icon-page-last:before {
  content: "\e6c9";
}

.wd-icon-next:before {
  content: "\e6ca";
}

.wd-icon-swap:before {
  content: "\e6cb";
}

.wd-icon-round:before {
  content: "\e6cc";
}

.wd-icon-previous:before {
  content: "\e6cd";
}

.wd-icon-enter:before {
  content: "\e6ce";
}

.wd-icon-chevron-down:before {
  content: "\e6cf";
}

.wd-icon-caret-down-small:before {
  content: "\e6d0";
}

.wd-icon-swap-right:before {
  content: "\e6d1";
}

.wd-icon-chevron-left-circle:before {
  content: "\e6d2";
}

.wd-icon-caret-left-small:before {
  content: "\e6d3";
}

.wd-icon-chevron-right-circle:before {
  content: "\e6d4";
}

.wd-icon-a-chevron-leftdouble:before {
  content: "\e6d5";
}

.wd-icon-chevron-left-rectangle:before {
  content: "\e6d6";
}

.wd-icon-a-chevron-rightdouble:before {
  content: "\e6d7";
}

.wd-icon-page-first:before {
  content: "\e6d8";
}

.wd-icon-forward:before {
  content: "\e6d9";
}

.wd-icon-view-column:before {
  content: "\e6b9";
}

.wd-icon-view-module:before {
  content: "\e6ba";
}

.wd-icon-format-vertical-align-right:before {
  content: "\e6bb";
}

.wd-icon-view-list:before {
  content: "\e6bc";
}

.wd-icon-order-descending:before {
  content: "\e6bd";
}

.wd-icon-format-horizontal-align-bottom:before {
  content: "\e6be";
}

.wd-icon-queue:before {
  content: "\e6bf";
}

.wd-icon-menu-fold:before {
  content: "\e6c0";
}

.wd-icon-menu-unfold:before {
  content: "\e6c1";
}

.wd-icon-format-horizontal-align-top:before {
  content: "\e6c2";
}

.wd-icon-a-rootlist:before {
  content: "\e6c3";
}

.wd-icon-order-ascending:before {
  content: "\e6c4";
}

.wd-icon-format-vertical-align-left:before {
  content: "\e6c5";
}

.wd-icon-format-horizontal-align-center:before {
  content: "\e6c6";
}

.wd-icon-format-vertical-align-center:before {
  content: "\e6c7";
}

.wd-icon-swap-left:before {
  content: "\e6c8";
}

.wd-icon-flag:before {
  content: "\e6aa";
}

.wd-icon-code:before {
  content: "\e6ab";
}

.wd-icon-cart:before {
  content: "\e6ac";
}

.wd-icon-attach:before {
  content: "\e6ad";
}

.wd-icon-chart:before {
  content: "\e6ae";
}

.wd-icon-creditcard:before {
  content: "\e6af";
}

.wd-icon-calendar:before {
  content: "\e6b0";
}

.wd-icon-app:before {
  content: "\e6b1";
}

.wd-icon-books:before {
  content: "\e6b2";
}

.wd-icon-barcode:before {
  content: "\e6b3";
}

.wd-icon-chart-pie:before {
  content: "\e6b4";
}

.wd-icon-chart-bar:before {
  content: "\e6b5";
}

.wd-icon-chart-bubble:before {
  content: "\e6b6";
}

.wd-icon-bulletpoint:before {
  content: "\e6b7";
}

.wd-icon-bianjiliebiao:before {
  content: "\e6b8";
}

.wd-icon-image:before {
  content: "\e69a";
}

.wd-icon-laptop:before {
  content: "\e69b";
}

.wd-icon-hourglass:before {
  content: "\e69c";
}

.wd-icon-call:before {
  content: "\e69d";
}

.wd-icon-mobile-vibrate:before {
  content: "\e69e";
}

.wd-icon-mail:before {
  content: "\e69f";
}

.wd-icon-notification-filled:before {
  content: "\e6a0";
}

.wd-icon-desktop:before {
  content: "\e6a1";
}

.wd-icon-history:before {
  content: "\e6a2";
}

.wd-icon-discount-filled:before {
  content: "\e6a3";
}

.wd-icon-dashboard:before {
  content: "\e6a4";
}

.wd-icon-discount:before {
  content: "\e6a5";
}

.wd-icon-heart-filled:before {
  content: "\e6a6";
}

.wd-icon-chat1:before {
  content: "\e6a7";
}

.wd-icon-a-controlplatform:before {
  content: "\e6a8";
}

.wd-icon-gift:before {
  content: "\e6a9";
}

.wd-icon-photo:before {
  content: "\e692";
}

.wd-icon-play-circle-stroke:before {
  content: "\e693";
}

.wd-icon-notification:before {
  content: "\e694";
}

.wd-icon-cloud:before {
  content: "\e695";
}

.wd-icon-gender-female:before {
  content: "\e696";
}

.wd-icon-fork:before {
  content: "\e697";
}

.wd-icon-layers:before {
  content: "\e698";
}

.wd-icon-lock-off:before {
  content: "\e699";
}

.wd-icon-location:before {
  content: "\e68a";
}

.wd-icon-mobile:before {
  content: "\e68b";
}

.wd-icon-qrcode:before {
  content: "\e68c";
}

.wd-icon-home1:before {
  content: "\e68d";
}

.wd-icon-time:before {
  content: "\e68e";
}

.wd-icon-heart:before {
  content: "\e68f";
}

.wd-icon-lock-on:before {
  content: "\e690";
}

.wd-icon-print:before {
  content: "\e691";
}

.wd-icon-slash:before {
  content: "\e67a";
}

.wd-icon-usb:before {
  content: "\e67b";
}

.wd-icon-tools:before {
  content: "\e67c";
}

.wd-icon-wifi:before {
  content: "\e67d";
}

.wd-icon-star-filled:before {
  content: "\e67e";
}

.wd-icon-server:before {
  content: "\e67f";
}

.wd-icon-sound:before {
  content: "\e680";
}

.wd-icon-a-precisemonitor:before {
  content: "\e681";
}

.wd-icon-service:before {
  content: "\e682";
}

.wd-icon-tips:before {
  content: "\e683";
}

.wd-icon-pin:before {
  content: "\e684";
}

.wd-icon-secured:before {
  content: "\e685";
}

.wd-icon-star:before {
  content: "\e686";
}

.wd-icon-gender-male:before {
  content: "\e687";
}

.wd-icon-shop:before {
  content: "\e688";
}

.wd-icon-money-circle:before {
  content: "\e689";
}

.wd-icon-file-word:before {
  content: "\e66a";
}

.wd-icon-file-unknown:before {
  content: "\e66b";
}

.wd-icon-folder-open:before {
  content: "\e66c";
}

.wd-icon-file-pdf:before {
  content: "\e66d";
}

.wd-icon-folder:before {
  content: "\e66e";
}

.wd-icon-folder-add:before {
  content: "\e66f";
}

.wd-icon-file:before {
  content: "\e670";
}

.wd-icon-file-image:before {
  content: "\e671";
}

.wd-icon-file-powerpoint:before {
  content: "\e672";
}

.wd-icon-file-add:before {
  content: "\e673";
}

.wd-icon-file-icon:before {
  content: "\e674";
}

.wd-icon-file-paste:before {
  content: "\e675";
}

.wd-icon-file-excel:before {
  content: "\e676";
}

.wd-icon-file-copy:before {
  content: "\e677";
}

.wd-icon-video1:before {
  content: "\e678";
}

.wd-icon-wallet:before {
  content: "\e679";
}

.wd-icon-ie:before {
  content: "\e65d";
}

.wd-icon-logo-codepen:before {
  content: "\e65e";
}

.wd-icon-github-filled:before {
  content: "\e65f";
}

.wd-icon-ie-filled:before {
  content: "\e660";
}

.wd-icon-apple:before {
  content: "\e661";
}

.wd-icon-windows-filled:before {
  content: "\e662";
}

.wd-icon-internet:before {
  content: "\e663";
}

.wd-icon-github:before {
  content: "\e664";
}

.wd-icon-windows:before {
  content: "\e665";
}

.wd-icon-apple-filled:before {
  content: "\e666";
}

.wd-icon-chrome-filled:before {
  content: "\e667";
}

.wd-icon-chrome:before {
  content: "\e668";
}

.wd-icon-android:before {
  content: "\e669";
}

.wd-icon-edit-outline:before {
  content: "\e64a";
}

.wd-icon-detection:before {
  content: "\e64b";
}

.wd-icon-check-outline:before {
  content: "\e64c";
}

.wd-icon-close:before {
  content: "\e64d";
}

.wd-icon-check:before {
  content: "\e64e";
}

.wd-icon-arrow-left:before {
  content: "\e64f";
}

.wd-icon-computer:before {
  content: "\e650";
}

.wd-icon-clock:before {
  content: "\e651";
}

.wd-icon-check-bold:before {
  content: "\e652";
}

.wd-icon-bags:before {
  content: "\e653";
}

.wd-icon-arrow-down:before {
  content: "\e654";
}

.wd-icon-arrow-right:before {
  content: "\e655";
}

.wd-icon-circle:before {
  content: "\e656";
}

.wd-icon-arrow-thin-down:before {
  content: "\e657";
}

.wd-icon-camera:before {
  content: "\e658";
}

.wd-icon-close-bold:before {
  content: "\e659";
}

.wd-icon-add-circle:before {
  content: "\e65a";
}

.wd-icon-arrow-thin-up:before {
  content: "\e65b";
}

.wd-icon-add:before {
  content: "\e65c";
}

.wd-icon-keyboard-delete:before {
  content: "\e634";
}

.wd-icon-transfer:before {
  content: "\e635";
}

.wd-icon-eye-close:before {
  content: "\e61f";
}

.wd-icon-delete:before {
  content: "\e61e";
}

.wd-icon-download:before {
  content: "\e636";
}

.wd-icon-picture:before {
  content: "\e637";
}

.wd-icon-refresh:before {
  content: "\e638";
}

.wd-icon-read:before {
  content: "\e639";
}

.wd-icon-note:before {
  content: "\e63a";
}

.wd-icon-phone:before {
  content: "\e63b";
}

.wd-icon-lenovo:before {
  content: "\e63c";
}

.wd-icon-home:before {
  content: "\e63d";
}

.wd-icon-search:before {
  content: "\e63e";
}

.wd-icon-fill-camera:before {
  content: "\e63f";
}

.wd-icon-fill-arrow-down:before {
  content: "\e640";
}

.wd-icon-arrow-up:before {
  content: "\e61d";
}

.wd-icon-delete-thin:before {
  content: "\e641";
}

.wd-icon-filter:before {
  content: "\e642";
}

.wd-icon-evaluation:before {
  content: "\e643";
}

.wd-icon-close-outline:before {
  content: "\e644";
}

.wd-icon-dong:before {
  content: "\e645";
}

.wd-icon-error-fill:before {
  content: "\e646";
}

.wd-icon-chat:before {
  content: "\e647";
}

.wd-icon-decrease:before {
  content: "\e648";
}

.wd-icon-copy:before {
  content: "\e649";
}

.wd-icon-setting:before {
  content: "\e621";
}

.wd-icon-subscribe:before {
  content: "\e622";
}

.wd-icon-jdm:before {
  content: "\e620";
}

.wd-icon-spool:before {
  content: "\e623";
}

.wd-icon-warning:before {
  content: "\e624";
}

.wd-icon-wifi-error:before {
  content: "\e625";
}

.wd-icon-star-on:before {
  content: "\e626";
}

.wd-icon-rotate:before {
  content: "\e627";
}

.wd-icon-translate-bold:before {
  content: "\e628";
}

.wd-icon-keyboard-collapse:before {
  content: "\e629";
}

.wd-icon-keywords:before {
  content: "\e62a";
}

.wd-icon-scan:before {
  content: "\e62b";
}

.wd-icon-view:before {
  content: "\e62c";
}

.wd-icon-phone-compute:before {
  content: "\e62d";
}

.wd-icon-video:before {
  content: "\e62e";
}

.wd-icon-thin-arrow-left:before {
  content: "\e62f";
}

.wd-icon-goods:before {
  content: "\e630";
}

.wd-icon-list:before {
  content: "\e631";
}

.wd-icon-warn-bold:before {
  content: "\e632";
}

.wd-icon-more:before {
  content: "\e633";
}